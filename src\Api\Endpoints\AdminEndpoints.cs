using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Api.Extensions;
using Api.Services;
using Shared.Common;
using Shared.DTOs;

namespace Api.Endpoints;

public static class AdminEndpoints
{
    public static void MapAdminAuthEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/auth").RequireAuthorization("AdminOnly").WithTags("Admin Auth");

        group.MapPost("/login", async (AdminLoginRequest request, AdminService adminService, HttpContext context) =>
        {
            var result = await adminService.LoginAsync(request, context);
            return result.ToHttpResult();
        }).WithSummary("管理员登录").WithDescription("使用用户名和密码进行管理员身份验证").AllowAnonymous();

        group.MapPost("/logout", async (HttpContext context) =>
        {
            await context.SignOutAsync("AdminScheme");
            return TypedResults.Ok(ApiResponse.Success());
        }).WithSummary("管理员登出").WithDescription("注销当前管理员会话");

        group.MapGet("/me", (HttpContext context) =>
        {
            return TypedResults.Ok(ApiResponse.Success());
        }).WithSummary("验证管理员会话").WithDescription("验证当前会话是否为有效的管理员会话");
    }

    public static void MapAdminDashboardEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/dashboard").RequireAuthorization("AdminOnly").WithTags("Admin Dashboard");

        group.MapGet("/", async (AdminService adminService) =>
        {
            var result = await adminService.GetDashboardDataAsync();
            return result.ToHttpResult();
        }).WithSummary("获取仪表盘数据").WithDescription("获取管理员仪表盘的完整统计数据和系统概览信息");

        group.MapGet("/health", async (AdminService adminService) =>
        {
            var result = await adminService.GetSystemHealthAsync();
            return result.ToHttpResult();
        }).WithSummary("系统健康检查").WithDescription("检查数据库、消息队列、工作节点等系统组件的运行状态");

        group.MapGet("/stats/system", async (string timeRange, DateTime? startDate, DateTime? endDate, AdminService adminService) =>
        {
            var result = await adminService.GetSystemStatsAsync(timeRange, startDate, endDate);
            return result.ToHttpResult();
        }).WithSummary("获取系统统计").WithDescription("获取系统性能统计数据，支持实时和历史数据查询，时间范围：realtime|1h|24h|7d|custom");

        group.MapGet("/stats/users", async (DateTime? startDate, DateTime? endDate, string? groupBy, AdminService adminService) =>
        {
            var result = await adminService.GetUserStatsAsync(startDate, endDate, groupBy);
            return result.ToHttpResult();
        }).WithSummary("获取用户统计").WithDescription("获取用户注册、活跃度等统计数据，支持按日/周/月分组");

        group.MapGet("/stats/tasks", async (DateTime? startDate, DateTime? endDate, string? groupBy, AdminService adminService) =>
        {
            var result = await adminService.GetTaskStatsAsync(startDate, endDate, groupBy);
            return result.ToHttpResult();
        }).WithSummary("获取任务统计").WithDescription("获取任务执行情况统计数据，支持按时间分组查看趋势");

        group.MapGet("/stats/workers", async (AdminService adminService) =>
        {
            var result = await adminService.GetWorkerStatsAsync();
            return result.ToHttpResult();
        }).WithSummary("获取工作节点统计").WithDescription("获取工作节点总数、在线数量、健康状态分布和负载统计信息");

        group.MapGet("/stats/proxies", async (AdminService adminService) =>
        {
            var result = await adminService.GetProxyStatsAsync();
            return result.ToHttpResult();
        }).WithSummary("获取代理统计").WithDescription("获取代理池总数、可用代理数量、使用率和健康状态统计");

        group.MapGet("/stats/content", async (string timeRange, AdminService adminService) =>
        {
            var result = await adminService.GetContentStatsAsync(timeRange);
            return result.ToHttpResult();
        }).WithSummary("获取内容统计").WithDescription("获取指定时间范围内的内容统计，包括热门视频和黑名单增长趋势");

        group.MapGet("/alerts", async (int page, int pageSize, AdminService adminService) =>
        {
            var result = await adminService.GetAlertsAsync(page, pageSize);
            return result.ToHttpResult();
        }).WithSummary("获取系统告警").WithDescription("分页获取系统告警记录，按时间倒序排列显示");

        group.MapPost("/alerts/{alertId}/resolve", async (Guid alertId, AdminService adminService) =>
        {
            var result = await adminService.ResolveAlertAsync(alertId);
            return result.ToHttpResult();
        }).WithSummary("解决告警").WithDescription("将指定告警标记为已解决状态");

        group.MapPost("/cleanup", async (AdminService adminService) =>
        {
            var result = await adminService.CleanupOldDataAsync();
            return result.ToHttpResult();
        }).WithSummary("清理过期数据").WithDescription("清理过期的监控数据、告警记录和临时文件");
    }

    public static void MapAdminUserEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/users").RequireAuthorization("AdminOnly").WithTags("Admin User Management");

        group.MapGet("/", async (int page, int pageSize, string? search, UserType? userType, UserAccountStatus? status, UserService userService) =>
        {
            var result = await userService.GetUsersAsync(page, pageSize, search, userType, status);
            return result.ToHttpResult();
        }).WithSummary("获取用户列表").WithDescription("分页获取系统用户列表，支持按用户类型、状态和关键词筛选");

        group.MapGet("/{userId}", async (Guid userId, UserService userService) =>
        {
            var result = await userService.GetUserDetailAsync(userId);
            return result.ToHttpResult();
        }).WithSummary("获取用户详情").WithDescription("获取指定用户的详细信息，包括统计数据和账户状态");

        group.MapPost("/{userId}/disable", async (Guid userId, UserService userService) =>
        {
            var result = await userService.DisableUserAsync(userId);
            return result.ToHttpResult();
        }).WithSummary("禁用用户").WithDescription("禁用指定用户账户，阻止其登录和使用系统");

        group.MapPost("/{userId}/enable", async (Guid userId, UserService userService) =>
        {
            var result = await userService.EnableUserAsync(userId);
            return result.ToHttpResult();
        }).WithSummary("启用用户").WithDescription("启用指定用户账户，恢复其正常使用权限");

        group.MapPost("/{userId}/reset-password", async (Guid userId, UserService userService) =>
        {
            var result = await userService.ResetUserPasswordAsync(userId);
            return result.ToHttpResult();
        }).WithSummary("重置用户密码").WithDescription("为指定用户生成新的随机密码并发送到其邮箱");

        group.MapPost("/{userId}/modify-plan", async (Guid userId, UpdateUserPlanRequest request, UserService userService) =>
        {
            var result = await userService.UpdateUserPlanAsync(userId, request);
            return result.ToHttpResult();
        }).WithSummary("修改用户套餐").WithDescription("修改指定用户的套餐类型和到期时间");

        group.MapGet("/{userId}/sessions", async (Guid userId, UserService userService) =>
        {
            var result = await userService.GetUserSessionsAsync(userId);
            return result.ToHttpResult();
        }).WithSummary("获取用户会话").WithDescription("获取指定用户的所有活跃会话信息");

        group.MapPost("/{userId}/sessions/revoke", async (Guid userId, UserService userService) =>
        {
            var result = await userService.RevokeUserSessionsAsync(userId);
            return result.ToHttpResult();
        }).WithSummary("撤销用户会话").WithDescription("撤销指定用户的所有活跃会话，强制其重新登录");

        group.MapGet("/{userId}/tasks", async (Guid userId, int page, int pageSize, UserService userService) =>
        {
            var result = await userService.GetUserTasksAsync(userId, page, pageSize);
            return result.ToHttpResult();
        }).WithSummary("获取用户任务").WithDescription("分页获取指定用户的任务历史记录");

        group.MapGet("/{userId}/content", async (Guid userId, int page, int pageSize, UserService userService) =>
        {
            var result = await userService.GetUserContentHistoryAsync(userId, page, pageSize);
            return result.ToHttpResult();
        }).WithSummary("获取用户内容记录").WithDescription("分页获取指定用户的内容访问和下载历史");

        group.MapGet("/{userId}/billing", async (Guid userId, UserService userService) =>
        {
            var result = await userService.GetUserBillingHistoryAsync(userId);
            return result.ToHttpResult();
        }).WithSummary("获取用户计费记录").WithDescription("获取指定用户的套餐购买和计费历史记录");
    }

    public static void MapAdminTaskEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/tasks").RequireAuthorization("AdminOnly").WithTags("Admin Task Management");

        group.MapGet("/", async (TaskService taskService, int page = 1, int pageSize = 20, WorkerTaskStatus? status = null,
            WorkerTaskType? taskType = null, DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await taskService.GetTasksAsync(page, pageSize, status, taskType, startDate, endDate);
            return result.ToHttpResult();
        }).WithSummary("获取任务列表").WithDescription("分页获取系统任务，支持按执行状态、任务类型和时间范围进行筛选");

        group.MapGet("/{taskId}", async (Guid taskId, TaskService taskService) =>
        {
            var result = await taskService.GetTaskDetailAsync(taskId);
            return result.ToHttpResult();
        }).WithSummary("获取任务详情").WithDescription("获取指定任务的详细信息，包括执行进度、日志和错误信息");

        group.MapPost("/{taskId}/cancel", async (Guid taskId, TaskService taskService) =>
        {
            var result = await taskService.CancelTaskAsync(taskId);
            return result.ToHttpResult();
        }).WithSummary("取消任务").WithDescription("强制取消指定任务的执行并更新其状态为已取消");

        group.MapPost("/{taskId}/retry", async (Guid taskId, TaskService taskService) =>
        {
            var result = await taskService.RetryTaskAsync(taskId);
            return result.ToHttpResult();
        }).WithSummary("重试任务").WithDescription("重新执行失败的任务，重置状态并重新加入执行队列");

        group.MapPost("/{taskId}/delete", async (Guid taskId, TaskService taskService) =>
        {
            var result = await taskService.DeleteTaskAsync(taskId);
            return result.ToHttpResult();
        }).WithSummary("删除任务记录").WithDescription("删除指定任务记录及其关联的文件和日志数据");

        group.MapPost("/cleanup", async (TaskService taskService, int daysOld = 30) =>
        {
            var result = await taskService.CleanupOldTasksAsync(daysOld);
            return result.ToHttpResult();
        }).WithSummary("清理过期任务").WithDescription("批量清理指定天数之前的已完成任务记录和相关文件");
    }

    public static void MapAdminWorkerEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/workers").RequireAuthorization("AdminOnly").WithTags("Admin Worker Management");

        group.MapPost("/register", async (WorkerRegisterRequest request, WorkerService workerService) =>
        {
            var result = await workerService.RegisterWorkerAsync(request);
            return result.ToHttpResult();
        }).WithSummary("工作节点注册").WithDescription("工作节点向系统注册，提供基础信息和硬件配置").AllowAnonymous();

        group.MapPost("/{workerId}/heartbeat", async (Guid workerId, WorkerHeartbeat heartbeat, WorkerService workerService) =>
        {
            var result = await workerService.UpdateHeartbeatAsync(workerId, heartbeat);
            return result.ToHttpResult();
        }).WithSummary("工作节点心跳").WithDescription("工作节点定期发送心跳信息，更新状态和性能指标").AllowAnonymous();

        group.MapPost("/{workerId}/unregister", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.UnregisterWorkerAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("工作节点注销").WithDescription("工作节点主动注销，清理相关数据和状态").AllowAnonymous();

        group.MapGet("/", async (WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerNodeListAsync();
            return result.ToHttpResult();
        }).WithSummary("获取工作节点列表").WithDescription("获取所有工作节点的基本信息和状态概览");

        group.MapGet("/{workerId}", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerByIdAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("获取工作节点详情").WithDescription("获取指定工作节点的详细信息，包括硬件配置和性能指标");

        group.MapGet("/{workerId}/tasks", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.GetActiveTaskCountAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("获取工作节点任务数").WithDescription("获取指定工作节点当前正在执行的任务数量");

        group.MapPost("/{workerId}/start", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.EnableWorkerNodeAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("启动工作节点").WithDescription("启用指定工作节点，使其开始接收和处理任务");

        group.MapPost("/{workerId}/stop", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.DisableWorkerNodeAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("停止工作节点").WithDescription("停用指定工作节点，完成当前任务后不再接收新任务");

        group.MapPost("/{workerId}/restart", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.RestartWorkerAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("重启工作节点").WithDescription("重启指定工作节点，先停止再启动以解决问题");

        group.MapPost("/{workerId}/health-check", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerByIdAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("健康检查工作节点").WithDescription("检查指定工作节点的连通性和运行状态");

        group.MapPost("/health-check", async (WorkerService workerService) =>
        {
            var result = await workerService.HealthCheckAllWorkersAsync();
            return result.ToHttpResult();
        }).WithSummary("健康检查所有工作节点").WithDescription("批量检查所有工作节点的健康状态和连通性");

        group.MapPost("/{workerId}/cleanup-files", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.CleanupWorkerFilesAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("清理工作节点文件").WithDescription("清理指定工作节点的临时文件和缓存数据");

        group.MapPost("/cleanup-files", async (WorkerService workerService) =>
        {
            var result = await workerService.CleanupAllWorkerFilesAsync();
            return result.ToHttpResult();
        }).WithSummary("清理所有工作节点文件").WithDescription("批量清理所有工作节点的临时文件和缓存数据");

        group.MapPost("/{workerId}/delete", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.DeleteWorkerAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("删除工作节点").WithDescription("删除指定工作节点及其相关数据和配置");
    }

    public static void MapAdminProxyEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/proxies").RequireAuthorization("AdminOnly").WithTags("Admin Proxy Management");

        group.MapGet("/", async (int page, int pageSize, string? status, string? healthStatus, ProxyService proxyService) =>
        {
            var result = await proxyService.GetProxyListAsync(page, pageSize, status, healthStatus);
            return result.ToHttpResult();
        }).WithSummary("获取代理列表").WithDescription("分页获取代理池列表，支持按状态和健康状态筛选");

        group.MapGet("/{proxyId}", async (Guid proxyId, ProxyService proxyService) =>
        {
            var result = await proxyService.GetProxyDetailAsync(proxyId);
            return result.ToHttpResult();
        }).WithSummary("获取代理详情").WithDescription("获取指定代理的详细信息，包括使用统计和健康状态");

        group.MapPost("/", async (AddProxyRequest request, ProxyService proxyService) =>
        {
            var result = await proxyService.AddProxyAsync(request);
            return result.ToHttpResult();
        }).WithSummary("添加代理").WithDescription("添加新的代理服务器到代理池");

        group.MapPost("/batch-add", async (BatchAddProxiesRequest request, ProxyService proxyService) =>
        {
            var result = await proxyService.BatchAddProxiesAsync(request);
            return result.ToHttpResult();
        }).WithSummary("批量添加代理").WithDescription("批量添加多个代理服务器到代理池");

        group.MapPost("/{proxyId}/update", async (Guid proxyId, UpdateProxyRequest request, ProxyService proxyService) =>
        {
            var result = await proxyService.UpdateProxyAsync(proxyId, request);
            return result.ToHttpResult();
        }).WithSummary("更新代理").WithDescription("更新指定代理的配置信息");

        group.MapPost("/{proxyId}/delete", async (Guid proxyId, ProxyService proxyService) =>
        {
            var result = await proxyService.DeleteProxyAsync(proxyId);
            return result.ToHttpResult();
        }).WithSummary("删除代理").WithDescription("从代理池中删除指定代理");

        group.MapPost("/batch-delete", async (BatchDeleteProxiesRequest request, ProxyService proxyService) =>
        {
            var result = await proxyService.BatchDeleteProxiesAsync(request);
            return result.ToHttpResult();
        }).WithSummary("批量删除代理").WithDescription("批量删除多个代理服务器");

        group.MapPost("/{proxyId}/enable", async (Guid proxyId, ProxyService proxyService) =>
        {
            var result = await proxyService.EnableProxyAsync(proxyId);
            return result.ToHttpResult();
        }).WithSummary("启用代理").WithDescription("启用指定代理，使其可用于请求");

        group.MapPost("/{proxyId}/disable", async (Guid proxyId, ProxyService proxyService) =>
        {
            var result = await proxyService.DisableProxyAsync(proxyId);
            return result.ToHttpResult();
        }).WithSummary("禁用代理").WithDescription("禁用指定代理，停止使用该代理");

        group.MapPost("/{proxyId}/health-check", async (Guid proxyId, ProxyService proxyService) =>
        {
            var result = await proxyService.HealthCheckProxyAsync(proxyId);
            return result.ToHttpResult();
        }).WithSummary("健康检查代理").WithDescription("检查指定代理的连通性和响应时间");

        group.MapPost("/health-check", async (ProxyService proxyService) =>
        {
            var result = await proxyService.HealthCheckAllProxiesAsync();
            return result.ToHttpResult();
        }).WithSummary("健康检查所有代理").WithDescription("批量检查所有代理的健康状态");
    }

    public static void MapAdminContentEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/content").RequireAuthorization("AdminOnly").WithTags("Admin Content Management");

        group.MapGet("/", async (YouTubeService youTubeService, int page = 1, int pageSize = 20, string? type = null, string? search = null,
            bool? isBlacklisted = null) =>
        {
            var result = await youTubeService.GetContentAsync(page, pageSize, type, search, isBlacklisted);
            return result.ToHttpResult();
        }).WithSummary("获取内容列表").WithDescription("分页获取YouTube内容记录，支持按类型、关键词和黑名单状态筛选");

        group.MapPost("/{contentType}/{contentId}/blacklist", async (string contentType, string contentId, YouTubeService youTubeService) =>
        {
            var result = await youTubeService.BlacklistContentAsync(contentType, contentId);
            return result.ToHttpResult();
        }).WithSummary("加入黑名单").WithDescription("将指定YouTube内容加入黑名单，阻止用户下载");

        group.MapPost("/{contentType}/{contentId}/unblacklist", async (string contentType, string contentId, YouTubeService youTubeService) =>
        {
            var result = await youTubeService.UnblacklistContentAsync(contentType, contentId);
            return result.ToHttpResult();
        }).WithSummary("移出黑名单").WithDescription("将指定YouTube内容从黑名单移除，恢复下载权限");
    }
}