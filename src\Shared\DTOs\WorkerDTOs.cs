using Shared.Common;

namespace Shared.DTOs;

public record WorkerResponse(
    Guid Id,
    string Name,
    string BaseUrl,
    string? MachineName,
    WorkerStatus Status,
    WorkerHealthStatus HealthStatus,
    WorkerHardwareInfo? HardwareInfo,
    int TotalProcessedTasks,
    int ConsecutiveFailures,
    DateTime CreatedAt,
    DateTime UpdatedAt,
    DateTime? LastActiveAt,
    DateTime? LastHealthCheckAt,
    DateTime? LastFailureAt,
    DateTime? LastStartAt);

public record WorkerHardwareInfo(
    int CpuCores,
    double TotalMemoryGB,
    double TotalDiskGB);

public record WorkerRegisterRequest(
    string BaseUrl,
    string? MachineName,
    WorkerHardwareInfo HardwareInfo,
    DateTime LastStartAt);

public record WorkerHeartbeat(
    WorkerStatus Status,
    WorkerRuntimeMetrics RuntimeMetrics,
    DateTime Timestamp);

public record WorkerRuntimeMetrics(
    double CpuUsagePercent,
    double MemoryUsagePercent,
    double DiskUsagePercent,
    int ActiveTasks,
    int TotalProcessedTasks,
    double NetworkReceivedGB,
    double NetworkSentGB,
    double NetworkBandwidthMbps,
    int ActiveConnections);

public record AddWorkerNodeRequest(
    string Name,
    string BaseUrl);

public record RegistrationRequest(
    string BaseUrl,
    string? MachineName,
    WorkerRuntimeMetrics Metrics,
    DateTime LastStartAt);

public record NodeHeartbeat(
    Guid NodeId,
    DateTime Timestamp,
    WorkerRuntimeMetrics Metrics,
    string Status);
