using FluentValidation;
using Shared.Common;

namespace Shared.DTOs;

public record ProxyInfo(string Host, int Port, string? Username = null, string? Password = null, ProxyType Type = ProxyType.Http);



public record UpdateProxyRequest(string Host, int Port, string? Username, string? Password, ProxyType Type, string? Notes);


public record ProxyDetailResponse(
    Guid Id,
    string Host,
    int Port,
    string? Username,
    ProxyType Type,
    ProxyStatus Status,
    ProxyHealthStatus HealthStatus,
    DateTime? LastUsedAt,
    DateTime? LastHealthCheckAt,
    int? ResponseTimeMs,
    int FailureCount,
    int SuccessCount,
    int UsageCount,
    string? ErrorMessage,
    DateTime CreatedAt,
    DateTime UpdatedAt,
    string? Notes);

public record ProxyStats(int TotalProxies, int ActiveProxies, int HealthyProxies, int UnhealthyProxies, double HealthRate, DateTime LastHealthCheck);

public record AddProxyRequest(string Host, int Port, string? Username, string? Password, ProxyType Type, string? Notes);

public record BatchAddProxiesRequest(List<AddProxyRequest> Proxies);

public record BatchDeleteProxiesRequest(List<Guid> ProxyIds);

public record ProxyListResponse(
    List<ProxyDetailResponse> Proxies,
    int TotalCount,
    int Page,
    int PageSize);

public record ProxyHealthCheckResponse(
    Guid ProxyId,
    bool IsHealthy,
    int? ResponseTimeMs,
    string? ErrorMessage,
    DateTime CheckedAt);