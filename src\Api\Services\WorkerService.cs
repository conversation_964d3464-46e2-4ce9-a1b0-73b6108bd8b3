using Api.Data;
using Api.Data.Models;
using Microsoft.EntityFrameworkCore;
using Shared.Common;

namespace Api.Services;

public class WorkerService(AppDbContext dbContext, HttpClient httpClient, ILogger<WorkerService> logger)
{
    public async Task<ServiceResult<WorkerResponse>> RegisterWorkerAsync(WorkerRegisterRequest request)
    {
        try
        {
            var existingWorker = await dbContext.Workers.FirstOrDefaultAsync(w => w.BaseUrl == request.BaseUrl);

            if (existingWorker != null)
            {
                existingWorker.MachineName = request.MachineName;
                existingWorker.CpuCores = request.HardwareInfo.CpuCores;
                existingWorker.TotalMemoryGB = request.HardwareInfo.TotalMemoryGB;
                existingWorker.TotalDiskGB = request.HardwareInfo.TotalDiskGB;
                existingWorker.Status = WorkerStatus.Online;
                existingWorker.HealthStatus = WorkerHealthStatus.Healthy;
                existingWorker.LastActiveAt = DateTime.UtcNow;
                existingWorker.LastStartAt = request.LastStartAt;
                existingWorker.UpdatedAt = DateTime.UtcNow;

                await dbContext.SaveChangesAsync();

                logger.LogInformation("工作节点重新注册成功: {WorkerId} - {BaseUrl}", existingWorker.Id, request.BaseUrl);

                return ServiceResult<WorkerResponse>.Success(MapToWorkerResponse(existingWorker));
            }

            var worker = new Worker
            {
                Id = Guid.NewGuid(),
                Name = $"Worker-{request.MachineName ?? "Unknown"}",
                BaseUrl = request.BaseUrl,
                MachineName = request.MachineName,
                CpuCores = request.HardwareInfo.CpuCores,
                TotalMemoryGB = request.HardwareInfo.TotalMemoryGB,
                TotalDiskGB = request.HardwareInfo.TotalDiskGB,
                Status = WorkerStatus.Online,
                HealthStatus = WorkerHealthStatus.Healthy,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                LastActiveAt = DateTime.UtcNow,
                LastStartAt = request.LastStartAt
            };

            dbContext.Workers.Add(worker);
            await dbContext.SaveChangesAsync();

            logger.LogInformation("新工作节点注册成功: {WorkerId} - {BaseUrl}", worker.Id, request.BaseUrl);

            return ServiceResult<WorkerResponse>.Success(MapToWorkerResponse(worker));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "注册工作节点时发生错误: {BaseUrl}", request.BaseUrl);
            return ServiceResult<WorkerResponse>.Failure("注册工作节点失败", "WORKER_REGISTER_ERROR");
        }
    }

    public async Task<ServiceResult> UpdateHeartbeatAsync(Guid workerId, WorkerHeartbeat heartbeat)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null)
            {
                logger.LogWarning("收到未知工作节点的心跳: {WorkerId}", workerId);
                return ServiceResult.Failure("工作节点不存在", "WORKER_NOT_FOUND");
            }

            worker.Status = heartbeat.Status;
            worker.LastActiveAt = heartbeat.Timestamp;
            worker.UpdatedAt = DateTime.UtcNow;

            var metrics = new WorkerMetrics
            {
                Id = Guid.NewGuid(),
                WorkerId = workerId,
                CpuUsagePercent = heartbeat.RuntimeMetrics.CpuUsagePercent,
                MemoryUsagePercent = heartbeat.RuntimeMetrics.MemoryUsagePercent,
                DiskUsagePercent = heartbeat.RuntimeMetrics.DiskUsagePercent,
                ActiveTasks = heartbeat.RuntimeMetrics.ActiveTasks,
                TotalProcessedTasks = heartbeat.RuntimeMetrics.TotalProcessedTasks,
                NetworkReceivedGB = heartbeat.RuntimeMetrics.NetworkReceivedGB,
                NetworkSentGB = heartbeat.RuntimeMetrics.NetworkSentGB,
                NetworkBandwidthMbps = heartbeat.RuntimeMetrics.NetworkBandwidthMbps,
                ActiveConnections = heartbeat.RuntimeMetrics.ActiveConnections,
                RecordedAt = heartbeat.Timestamp
            };

            dbContext.WorkerMetrics.Add(metrics);

            worker.HealthStatus = DetermineHealthStatus(heartbeat.RuntimeMetrics);

            await dbContext.SaveChangesAsync();

            logger.LogDebug("工作节点心跳更新成功: {WorkerId}", workerId);

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "更新工作节点心跳时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure("更新心跳失败", "HEARTBEAT_UPDATE_ERROR");
        }
    }

    public async Task<ServiceResult> MarkNodeOfflineAsync(Guid workerId)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null) return ServiceResult.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            worker.Status = WorkerStatus.Offline;
            worker.UpdatedAt = DateTime.UtcNow;

            await dbContext.SaveChangesAsync();

            logger.LogInformation("工作节点已标记为离线: {WorkerId}", workerId);

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "标记工作节点离线时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure("标记节点离线失败", "MARK_OFFLINE_ERROR");
        }
    }

    public async Task<ServiceResult<List<WorkerResponse>>> GetAllWorkersAsync()
    {
        try
        {
            var workers = await dbContext.Workers.OrderBy(w => w.Name).ToListAsync();

            var responses = workers.Select(MapToWorkerResponse).ToList();

            return ServiceResult<List<WorkerResponse>>.Success(responses);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取工作节点列表时发生错误");
            return ServiceResult<List<WorkerResponse>>.Failure("获取工作节点列表失败", "GET_WORKERS_ERROR");
        }
    }

    public async Task<ServiceResult<WorkerResponse>> GetWorkerByIdAsync(Guid workerId)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null) return ServiceResult<WorkerResponse>.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            return ServiceResult<WorkerResponse>.Success(MapToWorkerResponse(worker));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取工作节点信息时发生错误: {WorkerId}", workerId);
            return ServiceResult<WorkerResponse>.Failure("获取工作节点信息失败", "GET_WORKER_ERROR");
        }
    }

    public async Task<ServiceResult<Worker?>> GetAvailableWorkerAsync()
    {
        try
        {
            var worker = await dbContext.Workers.Where(w => w.Status == WorkerStatus.Online && w.HealthStatus == WorkerHealthStatus.Healthy)
                .OrderBy(w => w.TotalProcessedTasks).FirstOrDefaultAsync();

            return ServiceResult<Worker?>.Success(worker);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取可用工作节点时发生错误");
            return ServiceResult<Worker?>.Failure("获取可用工作节点失败", "GET_AVAILABLE_WORKER_ERROR");
        }
    }

    private static WorkerResponse MapToWorkerResponse(Worker worker)
    {
        var hardwareInfo = worker.CpuCores.HasValue && worker.TotalMemoryGB.HasValue && worker.TotalDiskGB.HasValue
            ? new WorkerHardwareInfo(worker.CpuCores.Value, worker.TotalMemoryGB.Value, worker.TotalDiskGB.Value)
            : null;

        return new WorkerResponse(worker.Id, worker.Name, worker.BaseUrl, worker.MachineName, worker.Status, worker.HealthStatus, hardwareInfo,
            worker.TotalProcessedTasks, worker.ConsecutiveFailures, worker.CreatedAt, worker.UpdatedAt, worker.LastActiveAt, worker.LastHealthCheckAt,
            worker.LastFailureAt, worker.LastStartAt);
    }

    public async Task<ServiceResult<int>> HealthCheckAllWorkersAsync()
    {
        try
        {
            var workers = await dbContext.Workers.Where(w => w.Status != WorkerStatus.Offline).ToListAsync();

            var healthyCount = 0;
            var tasks = new List<Task>();

            foreach (var worker in workers) tasks.Add(PerformHealthCheckAsync(worker));

            await Task.WhenAll(tasks);

            var updatedWorkers = await dbContext.Workers.Where(w => w.Status != WorkerStatus.Offline).ToListAsync();

            healthyCount = updatedWorkers.Count(w => w.HealthStatus == WorkerHealthStatus.Healthy);

            logger.LogInformation("健康检查完成，{HealthyCount}/{TotalCount} 个节点健康", healthyCount, updatedWorkers.Count);

            return ServiceResult<int>.Success(healthyCount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "执行工作节点健康检查时发生错误");
            return ServiceResult<int>.Failure("健康检查失败", "HEALTH_CHECK_ERROR");
        }
    }

    public async Task<ServiceResult> AssignTaskToWorkerAsync(Guid taskId, Guid workerId)
    {
        try
        {
            var task = await dbContext.WorkerTasks.FindAsync(taskId);
            if (task == null) return ServiceResult.Failure("任务不存在", "TASK_NOT_FOUND");

            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null) return ServiceResult.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            if (worker.Status != WorkerStatus.Online || worker.HealthStatus != WorkerHealthStatus.Healthy)
                return ServiceResult.Failure("工作节点不可用", "WORKER_UNAVAILABLE");

            task.WorkerId = workerId;
            task.Status = WorkerTaskStatus.Queued;
            task.UpdatedAt = DateTime.UtcNow;

            await dbContext.SaveChangesAsync();

            logger.LogInformation("任务已分配给工作节点: TaskId={TaskId}, WorkerId={WorkerId}", taskId, workerId);

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "分配任务给工作节点时发生错误: TaskId={TaskId}, WorkerId={WorkerId}", taskId, workerId);
            return ServiceResult.Failure("分配任务失败", "ASSIGN_TASK_ERROR");
        }
    }

    private async Task PerformHealthCheckAsync(Worker worker)
    {
        try
        {
            var response = await httpClient.GetAsync($"{worker.BaseUrl}/worker/status");

            if (response.IsSuccessStatusCode)
            {
                worker.HealthStatus = WorkerHealthStatus.Healthy;
                worker.LastHealthCheckAt = DateTime.UtcNow;
                worker.UpdatedAt = DateTime.UtcNow;

                if (worker.Status == WorkerStatus.Offline)
                {
                    worker.Status = WorkerStatus.Online;
                    logger.LogInformation("工作节点重新上线: {WorkerId}", worker.Id);
                }
            }
            else
            {
                worker.HealthStatus = WorkerHealthStatus.Critical;
                worker.LastHealthCheckAt = DateTime.UtcNow;
                worker.UpdatedAt = DateTime.UtcNow;
                logger.LogWarning("工作节点健康检查失败: {WorkerId}, 状态码: {StatusCode}", worker.Id, response.StatusCode);
            }

            await dbContext.SaveChangesAsync();
        }
        catch (HttpRequestException ex)
        {
            worker.Status = WorkerStatus.Offline;
            worker.HealthStatus = WorkerHealthStatus.Critical;
            worker.LastHealthCheckAt = DateTime.UtcNow;
            worker.UpdatedAt = DateTime.UtcNow;

            await dbContext.SaveChangesAsync();

            logger.LogError(ex, "工作节点健康检查网络错误: {WorkerId}", worker.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "工作节点健康检查时发生错误: {WorkerId}", worker.Id);
        }
    }

    public async Task<ServiceResult<List<WorkerResponse>>> GetOnlineWorkersAsync()
    {
        try
        {
            var workers = await dbContext.Workers.Where(w => w.Status == WorkerStatus.Online).OrderBy(w => w.Name).ToListAsync();

            var responses = workers.Select(MapToWorkerResponse).ToList();

            return ServiceResult<List<WorkerResponse>>.Success(responses);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取在线工作节点列表时发生错误");
            return ServiceResult<List<WorkerResponse>>.Failure("获取在线工作节点列表失败", "GET_ONLINE_WORKERS_ERROR");
        }
    }

    public async Task<ServiceResult> SetWorkerMaintenanceModeAsync(Guid workerId, bool maintenanceMode)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null) return ServiceResult.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            worker.Status = maintenanceMode ? WorkerStatus.Maintenance : WorkerStatus.Online;
            worker.UpdatedAt = DateTime.UtcNow;

            await dbContext.SaveChangesAsync();

            var action = maintenanceMode ? "进入" : "退出";
            logger.LogInformation("工作节点{Action}维护模式: {WorkerId}", action, workerId);

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "设置工作节点维护模式时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure("设置维护模式失败", "SET_MAINTENANCE_MODE_ERROR");
        }
    }

    public async Task<ServiceResult> UpdateWorkerFailureCountAsync(Guid workerId, bool increment = true)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null) return ServiceResult.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            if (increment)
            {
                worker.FailureCount++;
                worker.LastFailureAt = DateTime.UtcNow;
            }
            else
            {
                worker.FailureCount = Math.Max(0, worker.FailureCount - 1);
            }

            worker.UpdatedAt = DateTime.UtcNow;

            await dbContext.SaveChangesAsync();

            logger.LogDebug("工作节点失败计数已更新: {WorkerId}, 当前计数: {FailureCount}", workerId, worker.FailureCount);

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "更新工作节点失败计数时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure("更新失败计数失败", "UPDATE_FAILURE_COUNT_ERROR");
        }
    }

    public async Task<ServiceResult> UpdateWorkerProcessedTasksAsync(Guid workerId)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null) return ServiceResult.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            worker.TotalProcessedTasks++;
            worker.UpdatedAt = DateTime.UtcNow;

            await dbContext.SaveChangesAsync();

            logger.LogDebug("工作节点处理任务计数已更新: {WorkerId}, 总计数: {TotalProcessedTasks}", workerId, worker.TotalProcessedTasks);

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "更新工作节点处理任务计数时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure("更新处理任务计数失败", "UPDATE_PROCESSED_TASKS_ERROR");
        }
    }

    public async Task<ServiceResult<int>> GetActiveTaskCountAsync(Guid workerId)
    {
        try
        {
            var activeTaskCount = await dbContext.WorkerTasks
                .Where(t => t.WorkerId == workerId && (t.Status == WorkerTaskStatus.Processing || t.Status == WorkerTaskStatus.Queued)).CountAsync();

            return ServiceResult<int>.Success(activeTaskCount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取工作节点活跃任务数时发生错误: {WorkerId}", workerId);
            return ServiceResult<int>.Failure("获取活跃任务数失败", "GET_ACTIVE_TASK_COUNT_ERROR");
        }
    }

    public async Task<ServiceResult<List<WorkerResponse>>> GetWorkerNodeListAsync()
    {
        return await GetAllWorkersAsync();
    }

    public async Task<ServiceResult<WorkerListResponse>> GetWorkersAsync(int page, int pageSize, WorkerStatus? status, WorkerHealthStatus? healthStatus)
    {
        try
        {
            var query = dbContext.Workers.AsQueryable();

            if (status.HasValue)
                query = query.Where(w => w.Status == status.Value);

            if (healthStatus.HasValue)
                query = query.Where(w => w.HealthStatus == healthStatus.Value);

            var totalCount = await query.CountAsync();
            var skip = (page - 1) * pageSize;

            var workers = await query
                .OrderByDescending(w => w.LastActiveAt)
                .Skip(skip)
                .Take(pageSize)
                .ToListAsync();

            var workerResponses = workers.Select(MapToWorkerResponse).ToList();
            var response = new WorkerListResponse(workerResponses, totalCount, page, pageSize);

            return ServiceResult<WorkerListResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取工作节点列表时发生错误");
            return ServiceResult<WorkerListResponse>.Failure("获取工作节点列表失败");
        }
    }

    public async Task<ServiceResult<WorkerDetailResponse>> GetWorkerDetailAsync(Guid workerId)
    {
        try
        {
            var worker = await dbContext.Workers
                .Include(w => w.Metrics.OrderByDescending(m => m.RecordedAt).Take(10))
                .Include(w => w.AssignedTasks.Where(t => t.Status == WorkerTaskStatus.Processing))
                .FirstOrDefaultAsync(w => w.Id == workerId);

            if (worker == null)
                return ServiceResult<WorkerDetailResponse>.Failure("工作节点不存在");

            var recentMetrics = worker.Metrics.Select(m => new WorkerMetricResponse(
                m.CpuUsagePercent,
                m.MemoryUsagePercent,
                m.DiskUsagePercent,
                m.NetworkReceivedGB,
                m.NetworkSentGB,
                m.NetworkBandwidthMbps,
                m.ActiveConnections,
                m.RecordedAt)).ToList();

            var activeTasks = worker.AssignedTasks.Select(t => new WorkerTaskSummary(
                t.Id,
                t.Name,
                t.Status,
                t.Progress,
                t.CreatedAt)).ToList();

            var response = new WorkerDetailResponse(
                worker.Id,
                worker.Name,
                worker.BaseUrl,
                worker.MachineName,
                worker.CpuCores,
                worker.TotalMemoryGB,
                worker.TotalDiskGB,
                worker.Status,
                worker.HealthStatus,
                worker.TotalProcessedTasks,
                worker.ConsecutiveFailures,
                worker.LastActiveAt,
                worker.CreatedAt,
                recentMetrics,
                activeTasks);

            return ServiceResult<WorkerDetailResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取工作节点详情时发生错误: {WorkerId}", workerId);
            return ServiceResult<WorkerDetailResponse>.Failure("获取工作节点详情失败");
        }
    }

    public async Task<ServiceResult> UpdateWorkerAsync(Guid workerId, UpdateWorkerRequest request)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null)
                return ServiceResult.Failure("工作节点不存在");

            worker.Name = request.Name;
            worker.UpdatedAt = DateTime.UtcNow;

            await dbContext.SaveChangesAsync();

            logger.LogInformation("工作节点已更新: {WorkerId}", workerId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "更新工作节点时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure("更新工作节点失败");
        }
    }

    public async Task<ServiceResult> DeleteWorkerAsync(Guid workerId)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null)
                return ServiceResult.Failure("工作节点不存在");

            var activeTasks = await dbContext.WorkerTasks
                .Where(t => t.WorkerId == workerId &&
                           (t.Status == WorkerTaskStatus.Processing || t.Status == WorkerTaskStatus.Queued))
                .CountAsync();

            if (activeTasks > 0)
                return ServiceResult.Failure("工作节点有活跃任务，无法删除");

            dbContext.Workers.Remove(worker);
            await dbContext.SaveChangesAsync();

            logger.LogInformation("工作节点已删除: {WorkerId}", workerId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "删除工作节点时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure("删除工作节点失败");
        }
    }

    public async Task<ServiceResult<WorkerMetricsResponse>> GetWorkerMetricsAsync(Guid workerId, DateTime? startDate, DateTime? endDate)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null)
                return ServiceResult<WorkerMetricsResponse>.Failure("工作节点不存在");

            var query = dbContext.WorkerMetrics.Where(m => m.WorkerId == workerId);

            if (startDate.HasValue)
                query = query.Where(m => m.RecordedAt >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(m => m.RecordedAt <= endDate.Value);

            var metrics = await query
                .OrderByDescending(m => m.RecordedAt)
                .Take(100)
                .Select(m => new WorkerMetricResponse(
                    m.CpuUsagePercent,
                    m.MemoryUsagePercent,
                    m.DiskUsagePercent,
                    m.NetworkReceivedGB,
                    m.NetworkSentGB,
                    m.NetworkBandwidthMbps,
                    m.ActiveConnections,
                    m.RecordedAt))
                .ToListAsync();

            var response = new WorkerMetricsResponse(workerId, metrics);
            return ServiceResult<WorkerMetricsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取工作节点监控数据时发生错误: {WorkerId}", workerId);
            return ServiceResult<WorkerMetricsResponse>.Failure("获取监控数据失败");
        }
    }

    public async Task<ServiceResult> HealthCheckAllWorkersAsync()
    {
        try
        {
            var workers = await dbContext.Workers.ToListAsync();
            var healthCheckTasks = workers.Select(async worker =>
            {
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                    var response = await httpClient.GetAsync($"{worker.BaseUrl}/health", cts.Token);

                    worker.HealthStatus = response.IsSuccessStatusCode ? WorkerHealthStatus.Healthy : WorkerHealthStatus.Critical;
                    worker.LastActiveAt = DateTime.UtcNow;
                }
                catch
                {
                    worker.HealthStatus = WorkerHealthStatus.Critical;
                    worker.Status = WorkerStatus.Offline;
                }
                worker.UpdatedAt = DateTime.UtcNow;
            });

            await Task.WhenAll(healthCheckTasks);
            await dbContext.SaveChangesAsync();

            logger.LogInformation("已完成所有工作节点健康检查");
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "批量健康检查时发生错误");
            return ServiceResult.Failure("批量健康检查失败");
        }
    }

    public async Task<ServiceResult> CleanupWorkerFilesAsync(Guid workerId)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null)
                return ServiceResult.Failure("工作节点不存在");

            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
            var response = await httpClient.PostAsync($"{worker.BaseUrl}/cleanup", null, cts.Token);

            if (!response.IsSuccessStatusCode)
                return ServiceResult.Failure("清理文件请求失败");

            logger.LogInformation("工作节点文件清理完成: {WorkerId}", workerId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "清理工作节点文件时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure("清理文件失败");
        }
    }

    public async Task<ServiceResult> CleanupAllWorkerFilesAsync()
    {
        try
        {
            var workers = await dbContext.Workers
                .Where(w => w.Status == WorkerStatus.Online)
                .ToListAsync();

            var cleanupTasks = workers.Select(async worker =>
            {
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                    await httpClient.PostAsync($"{worker.BaseUrl}/cleanup", null, cts.Token);
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, "清理工作节点 {WorkerId} 文件失败", worker.Id);
                }
            });

            await Task.WhenAll(cleanupTasks);

            logger.LogInformation("已完成所有工作节点文件清理");
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "批量清理文件时发生错误");
            return ServiceResult.Failure("批量清理文件失败");
        }
    }

    public async Task<ServiceResult> CleanupInactiveWorkersAsync(int daysInactive)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-daysInactive);
            var inactiveWorkers = await dbContext.Workers
                .Where(w => w.LastActiveAt < cutoffDate && w.Status == WorkerStatus.Offline)
                .ToListAsync();

            dbContext.Workers.RemoveRange(inactiveWorkers);
            await dbContext.SaveChangesAsync();

            logger.LogInformation("清理了 {Count} 个不活跃工作节点", inactiveWorkers.Count);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "清理不活跃工作节点时发生错误");
            return ServiceResult.Failure("清理不活跃工作节点失败");
        }
    }

    public async Task<ServiceResult<WorkerStatsResponse>> GetWorkerNodeStatsAsync()
    {
        try
        {
            var workers = await dbContext.Workers.ToListAsync();

            var stats = new WorkerStatsResponse(workers.Count, workers.Count(w => w.Status == WorkerStatus.Online),
                workers.Count(w => w.Status == WorkerStatus.Offline), workers.Count(w => w.HealthStatus == WorkerHealthStatus.Healthy),
                workers.Count(w => w.HealthStatus == WorkerHealthStatus.Warning), workers.Count(w => w.HealthStatus == WorkerHealthStatus.Critical),
                workers.Sum(w => w.TotalProcessedTasks), workers.Sum(w => w.FailureCount));

            return ServiceResult<WorkerStatsResponse>.Success(stats);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取工作节点统计时发生错误");
            return ServiceResult<WorkerStatsResponse>.Failure("获取工作节点统计失败", "GET_WORKER_STATS_ERROR");
        }
    }

    public async Task<ServiceResult<WorkerPerformanceResponse>> GetWorkerNodePerformanceAsync()
    {
        try
        {
            var workers = await dbContext.Workers.Where(w => w.Status != WorkerStatus.Offline).ToListAsync();

            var latestMetrics = await dbContext.WorkerMetrics.Where(m => workers.Select(w => w.Id).Contains(m.WorkerId)).GroupBy(m => m.WorkerId)
                .Select(g => g.OrderByDescending(m => m.RecordedAt).First()).ToListAsync();

            var avgCpu = latestMetrics.Any() ? latestMetrics.Average(m => m.CpuUsagePercent) : 0;
            var avgMemory = latestMetrics.Any() ? latestMetrics.Average(m => m.MemoryUsagePercent) : 0;
            var avgDisk = latestMetrics.Any() ? latestMetrics.Average(m => m.DiskUsagePercent) : 0;
            var totalActiveTasks = latestMetrics.Sum(m => m.ActiveTasks);

            var performance = new WorkerPerformanceResponse(avgCpu, avgMemory, avgDisk, totalActiveTasks,
                latestMetrics.Any() ? latestMetrics.Max(m => m.RecordedAt) : DateTime.UtcNow);

            return ServiceResult<WorkerPerformanceResponse>.Success(performance);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取工作节点性能统计时发生错误");
            return ServiceResult<WorkerPerformanceResponse>.Failure("获取工作节点性能统计失败", "GET_WORKER_PERFORMANCE_ERROR");
        }
    }

    public async Task<ServiceResult<WorkerResponse>> AddWorkerNodeAsync(AddWorkerNodeRequest request)
    {
        try
        {
            var existingWorker = await dbContext.Workers.FirstOrDefaultAsync(w => w.BaseUrl == request.BaseUrl);

            if (existingWorker != null) return ServiceResult<WorkerResponse>.Failure("该URL的工作节点已存在", "WORKER_ALREADY_EXISTS");

            var worker = new Worker
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                BaseUrl = request.BaseUrl,
                Status = WorkerStatus.Offline,
                HealthStatus = WorkerHealthStatus.Unknown,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            dbContext.Workers.Add(worker);
            await dbContext.SaveChangesAsync();

            logger.LogInformation("手动添加工作节点成功: {WorkerId} - {Name}", worker.Id, worker.Name);

            return ServiceResult<WorkerResponse>.Success(MapToWorkerResponse(worker));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "添加工作节点时发生错误: {Name} - {BaseUrl}", request.Name, request.BaseUrl);
            return ServiceResult<WorkerResponse>.Failure("添加工作节点失败", "ADD_WORKER_ERROR");
        }
    }

    // ==================== 新增方法占位符 ====================

    public async Task<ServiceResult> EnableWorkerNodeAsync(Guid nodeId)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(nodeId);
            if (worker == null) return ServiceResult.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            worker.Status = WorkerStatus.Online;
            worker.UpdatedAt = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();

            logger.LogInformation("工作节点已启用: {WorkerId}", nodeId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "启用工作节点时发生错误: {WorkerId}", nodeId);
            return ServiceResult.Failure("启用工作节点失败", "ENABLE_WORKER_ERROR");
        }
    }

    public async Task<ServiceResult> DisableWorkerNodeAsync(Guid nodeId)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(nodeId);
            if (worker == null) return ServiceResult.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            worker.Status = WorkerStatus.Offline;
            worker.UpdatedAt = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();

            logger.LogInformation("工作节点已禁用: {WorkerId}", nodeId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "禁用工作节点时发生错误: {WorkerId}", nodeId);
            return ServiceResult.Failure("禁用工作节点失败", "DISABLE_WORKER_ERROR");
        }
    }

    public async Task<ServiceResult<bool>> ToggleWorkerNodeAsync(Guid nodeId)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(nodeId);
            if (worker == null) return ServiceResult<bool>.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            var newStatus = worker.Status == WorkerStatus.Online ? WorkerStatus.Offline : WorkerStatus.Online;
            worker.Status = newStatus;
            worker.UpdatedAt = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();

            var isEnabled = newStatus == WorkerStatus.Online;
            logger.LogInformation("工作节点状态已切换: {WorkerId} -> {Status}", nodeId, newStatus);
            return ServiceResult<bool>.Success(isEnabled);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "切换工作节点状态时发生错误: {WorkerId}", nodeId);
            return ServiceResult<bool>.Failure("切换工作节点状态失败", "TOGGLE_WORKER_ERROR");
        }
    }

    public async Task<ServiceResult<bool>> ToggleWorkerNodeAsync(Guid nodeId, bool isActive)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(nodeId);
            if (worker == null) return ServiceResult<bool>.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            worker.Status = isActive ? WorkerStatus.Online : WorkerStatus.Offline;
            worker.UpdatedAt = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();

            logger.LogInformation("工作节点状态已设置: {WorkerId} -> {Status}", nodeId, worker.Status);
            return ServiceResult<bool>.Success(isActive);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "设置工作节点状态时发生错误: {WorkerId}", nodeId);
            return ServiceResult<bool>.Failure("设置工作节点状态失败", "SET_WORKER_STATUS_ERROR");
        }
    }

    public async Task<ServiceResult<WorkerNodeDeletionSummary>> DeleteWorkerNodeAsync(Guid nodeId, bool force = false)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(nodeId);
            if (worker == null) return ServiceResult<WorkerNodeDeletionSummary>.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            // 检查是否有活跃任务
            var activeTasks = await dbContext.WorkerTasks
                .Where(t => t.WorkerId == nodeId && (t.Status == WorkerTaskStatus.Processing || t.Status == WorkerTaskStatus.Queued)).CountAsync();

            if (activeTasks > 0 && !force)
                return ServiceResult<WorkerNodeDeletionSummary>.Failure($"工作节点有 {activeTasks} 个活跃任务，请使用 force=true 强制删除", "WORKER_HAS_ACTIVE_TASKS");

            // 删除相关数据
            var deletedTasks = await dbContext.WorkerTasks.Where(t => t.WorkerId == nodeId).CountAsync();
            var deletedMetrics = await dbContext.WorkerMetrics.Where(m => m.WorkerId == nodeId).CountAsync();
            var deletedAlerts = await dbContext.WorkerAlerts.Where(a => a.WorkerId == nodeId).CountAsync();

            dbContext.WorkerTasks.RemoveRange(dbContext.WorkerTasks.Where(t => t.WorkerId == nodeId));
            dbContext.WorkerMetrics.RemoveRange(dbContext.WorkerMetrics.Where(m => m.WorkerId == nodeId));
            dbContext.WorkerAlerts.RemoveRange(dbContext.WorkerAlerts.Where(a => a.WorkerId == nodeId));
            dbContext.Workers.Remove(worker);

            await dbContext.SaveChangesAsync();

            var summary = new WorkerNodeDeletionSummary(nodeId, worker.Name, deletedTasks, deletedMetrics, deletedAlerts, force);

            logger.LogInformation("工作节点已删除: {WorkerId}, 删除任务: {Tasks}, 指标: {Metrics}, 告警: {Alerts}", nodeId, deletedTasks, deletedMetrics, deletedAlerts);

            return ServiceResult<WorkerNodeDeletionSummary>.Success(summary);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "删除工作节点时发生错误: {WorkerId}", nodeId);
            return ServiceResult<WorkerNodeDeletionSummary>.Failure("删除工作节点失败", "DELETE_WORKER_ERROR");
        }
    }

    public async Task<ServiceResult<WorkerRestartResult>> RestartWorkerAsync(Guid nodeId)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(nodeId);
            if (worker == null) return ServiceResult<WorkerRestartResult>.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            // TODO: 实现向工作节点发送重启命令的逻辑
            // 这里应该调用工作节点的重启API

            var result = new WorkerRestartResult(nodeId, worker.Name, "重启命令已发送", DateTime.UtcNow);

            logger.LogInformation("重启命令已发送到工作节点: {WorkerId}", nodeId);
            return ServiceResult<WorkerRestartResult>.Success(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "重启工作节点时发生错误: {WorkerId}", nodeId);
            return ServiceResult<WorkerRestartResult>.Failure("重启工作节点失败", "RESTART_WORKER_ERROR");
        }
    }



    private static WorkerHealthStatus DetermineHealthStatus(WorkerRuntimeMetrics metrics)
    {
        if (metrics.CpuUsagePercent > 90 || metrics.MemoryUsagePercent > 90 || metrics.DiskUsagePercent > 95)
            return WorkerHealthStatus.Critical;

        if (metrics.CpuUsagePercent > 70 || metrics.MemoryUsagePercent > 70 || metrics.DiskUsagePercent > 80)
            return WorkerHealthStatus.Warning;

        return WorkerHealthStatus.Healthy;
    }

    public async Task<ServiceResult> UnregisterWorkerAsync(Guid workerId)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null)
                return ServiceResult.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            worker.Status = WorkerStatus.Offline;
            worker.UpdatedAt = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();

            logger.LogInformation("工作节点已注销: {WorkerId}", workerId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "注销工作节点时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure("注销工作节点失败", "WORKER_UNREGISTER_ERROR");
        }
    }

    public async Task<ServiceResult> CleanupWorkerFilesAsync(Guid workerId)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null)
                return ServiceResult.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            // TODO: 实现清理指定工作节点文件的逻辑
            // 1. 调用工作节点的清理API
            // 2. 清理数据库中的相关文件记录
            // 3. 清理临时文件和缓存

            logger.LogInformation("工作节点文件清理完成: {WorkerId}", workerId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "清理工作节点文件时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure("清理工作节点文件失败", "CLEANUP_WORKER_FILES_ERROR");
        }
    }

    public async Task<ServiceResult<WorkerCleanupSummary>> CleanupAllWorkerFilesAsync()
    {
        try
        {
            var workers = await dbContext.Workers.Where(w => w.Status != WorkerStatus.Offline).ToListAsync();
            var summary = new WorkerCleanupSummary(0, 0, 0);

            foreach (var worker in workers)
            {
                var result = await CleanupWorkerFilesAsync(worker.Id);
                if (result.IsSuccess)
                    summary = summary with { SuccessCount = summary.SuccessCount + 1 };
                else
                    summary = summary with { FailureCount = summary.FailureCount + 1 };
            }

            summary = summary with { TotalCount = workers.Count };

            logger.LogInformation("批量清理工作节点文件完成: 总数={Total}, 成功={Success}, 失败={Failure}", summary.TotalCount, summary.SuccessCount, summary.FailureCount);

            return ServiceResult<WorkerCleanupSummary>.Success(summary);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "批量清理工作节点文件时发生错误");
            return ServiceResult<WorkerCleanupSummary>.Failure("批量清理工作节点文件失败", "CLEANUP_ALL_WORKER_FILES_ERROR");
        }
    }

    public Task<ServiceResult<YouTubeVideoResponse>> ParseVideoAsync(string videoId)
    {
        // TODO: 调用真实Worker解析视频
        logger.LogInformation("占位符：正在解析视频 {VideoId}", videoId);
        var sampleResponse = new YouTubeVideoResponse(
            videoId,
            "示例视频标题",
            "示例频道ID",
            "示例频道名称",
            $"https://www.youtube.com/channel/示例频道ID",
            "这是一个示例视频描述.",
            $"https://i.ytimg.com/vi/{videoId}/hqdefault.jpg",
            3600,
            DateTime.UtcNow.AddDays(-30),
            1000000,
            50000,
            5000,
            new List<VideoFormatDto>(),
            new List<AudioFormatDto>(),
            new List<SubtitleFormatDto>(),
            new List<string>()
        );
        return Task.FromResult(ServiceResult<YouTubeVideoResponse>.Success(sampleResponse));
    }

    public Task<ServiceResult<YouTubePlaylistResponse>> ParsePlaylistAsync(string playlistId, int? maxVideos)
    {
        // TODO: 调用真实Worker解析播放列表
        logger.LogInformation("占位符：正在解析播放列表 {PlaylistId}", playlistId);
        var sampleResponse = new YouTubePlaylistResponse(
            playlistId,
            "示例播放列表标题",
            "这是一个示例播放列表描述.",
            "示例频道ID",
            "示例频道名称",
            10,
            null,
            new List<PlaylistVideoInfo>()
        );
        return Task.FromResult(ServiceResult<YouTubePlaylistResponse>.Success(sampleResponse));
    }

    public Task<ServiceResult<YouTubeChannelResponse>> ParseChannelAsync(string channelId, int? maxVideos)
    {
        // TODO: 调用真实Worker解析频道
        logger.LogInformation("占位符：正在解析频道 {ChannelId}", channelId);
        var sampleResponse = new YouTubeChannelResponse(
            channelId,
            "示例频道标题",
            "这是一个示例频道描述.",
            100000,
            50,
            null,
            new List<ChannelVideoInfo>()
        );
        return Task.FromResult(ServiceResult<YouTubeChannelResponse>.Success(sampleResponse));
    }
}