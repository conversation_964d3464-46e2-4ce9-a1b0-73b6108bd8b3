using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Api.Data;
using Api.Data.Models;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class AdminService(IConfiguration configuration, AppDbContext dbContext, ILogger<AdminService> logger)
{
    private readonly string _adminEmail = configuration["AdminAuth:Email"] ?? "";
    private readonly string _adminPassword = configuration["AdminAuth:Password"] ?? "";

    public async Task<ServiceResult> LoginAsync(AdminLoginRequest request, HttpContext httpContext)
    {
        if (!string.Equals(request.Email, _adminEmail, StringComparison.OrdinalIgnoreCase) || request.Password != _adminPassword)
        {
            return ServiceResult.Failure(new Error(ErrorType.Unauthorized, ErrorKeys.INVALID_CREDENTIALS, DeveloperMessage: "管理员凭据无效。"));
        }

        var claims = new List<Claim>
        {
            new(ClaimTypes.Name, "admin"),
            new(ClaimTypes.Email, _adminEmail)
        };
        var claimsIdentity = new ClaimsIdentity(claims, "AdminScheme");
        var authProperties = new AuthenticationProperties
        {
            IsPersistent = true,
            ExpiresUtc = DateTimeOffset.UtcNow.AddHours(8)
        };

        await httpContext.SignInAsync("AdminScheme", new ClaimsPrincipal(claimsIdentity), authProperties);

        return ServiceResult.Success();
    }



    public async Task<ServiceResult<DashboardDataResponse>> GetDashboardDataAsync()
    {
        try
        {
            var systemOverview = await GetSystemOverviewAsync();
            var componentStatuses = await GetComponentStatusesAsync();
            var recentActivities = await GetRecentActivitiesAsync();
            var recentAlerts = await GetRecentAlertsAsync();

            var dashboardData = new DashboardDataResponse(
                systemOverview,
                componentStatuses,
                recentActivities,
                recentAlerts);

            return ServiceResult<DashboardDataResponse>.Success(dashboardData);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取仪表盘数据时发生错误");
            return ServiceResult<DashboardDataResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<SystemHealthResponse>> GetSystemHealthAsync()
    {
        try
        {
            var healthChecks = new List<HealthCheckResult>();

            var dbHealthCheck = await CheckDatabaseHealthAsync();
            healthChecks.Add(dbHealthCheck);

            var workerHealthCheck = await CheckWorkersHealthAsync();
            healthChecks.Add(workerHealthCheck);

            var proxyHealthCheck = await CheckProxiesHealthAsync();
            healthChecks.Add(proxyHealthCheck);

            var isHealthy = healthChecks.All(h => h.IsHealthy);

            var response = new SystemHealthResponse(isHealthy, healthChecks, DateTime.UtcNow);
            return ServiceResult<SystemHealthResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取系统健康状态时发生错误");
            return ServiceResult<SystemHealthResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<SystemStatsResponse>> GetSystemStatsAsync(string timeRange, DateTime? startDate, DateTime? endDate)
    {
        try
        {
            var cpuStats = new CpuStatsResponse(
                UsagePercentage: Random.Shared.NextDouble() * 100,
                CoreCount: Environment.ProcessorCount,
                LoadAverage: Random.Shared.NextDouble() * 2);

            var totalMemory = GC.GetTotalMemory(false);
            var memoryStats = new MemoryStatsResponse(
                TotalBytes: totalMemory * 4,
                UsedBytes: totalMemory,
                AvailableBytes: totalMemory * 3,
                UsagePercentage: 25.0);

            var diskStats = new DiskStatsResponse(
                TotalBytes: 1000L * 1024 * 1024 * 1024,
                UsedBytes: 250L * 1024 * 1024 * 1024,
                AvailableBytes: 750L * 1024 * 1024 * 1024,
                UsagePercentage: 25.0);

            var networkStats = new NetworkStatsResponse(
                BytesReceived: Random.Shared.NextInt64(1000000, 10000000),
                BytesSent: Random.Shared.NextInt64(1000000, 10000000),
                PacketsReceived: Random.Shared.NextInt64(10000, 100000),
                PacketsSent: Random.Shared.NextInt64(10000, 100000));

            var response = new SystemStatsResponse(cpuStats, memoryStats, diskStats, networkStats, DateTime.UtcNow);
            return ServiceResult<SystemStatsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取系统统计数据时发生错误");
            return ServiceResult<SystemStatsResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<UserStatsResponse>> GetUserStatsAsync(DateTime? startDate, DateTime? endDate, string? groupBy)
    {
        try
        {
            var totalUsers = await dbContext.Users.CountAsync();
            var today = DateTime.UtcNow.Date;

            var newUsersToday = await dbContext.Users
                .CountAsync(u => u.CreatedAt.Date == today);

            var activeUsersToday = await dbContext.Users
                .CountAsync(u => u.LastActiveAt.HasValue && u.LastActiveAt.Value.Date == today);

            var registeredUsers = await dbContext.Users
                .CountAsync(u => u.UserType == UserType.Registered);

            var anonymousUsers = await dbContext.Users
                .CountAsync(u => u.UserType == UserType.Anonymous);

            var growthData = await GetUserGrowthDataAsync(startDate, endDate, groupBy);

            var response = new UserStatsResponse(
                totalUsers,
                newUsersToday,
                activeUsersToday,
                registeredUsers,
                anonymousUsers,
                growthData);

            return ServiceResult<UserStatsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户统计数据时发生错误");
            return ServiceResult<UserStatsResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<TaskStatsResponse>> GetTaskStatsAsync(DateTime? startDate, DateTime? endDate, string? groupBy)
    {
        try
        {
            var query = dbContext.WorkerTasks.AsQueryable();

            if (startDate.HasValue)
                query = query.Where(t => t.CreatedAt >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(t => t.CreatedAt <= endDate.Value);

            var totalTasks = await query.CountAsync();
            var pendingTasks = await query.CountAsync(t => t.Status == WorkerTaskStatus.Pending);
            var processingTasks = await query.CountAsync(t => t.Status == WorkerTaskStatus.Processing);
            var completedTasks = await query.CountAsync(t => t.Status == WorkerTaskStatus.Completed);
            var failedTasks = await query.CountAsync(t => t.Status == WorkerTaskStatus.Failed);
            var cancelledTasks = await query.CountAsync(t => t.Status == WorkerTaskStatus.Cancelled);

            var response = new TaskStatsResponse(totalTasks, pendingTasks, processingTasks, completedTasks, failedTasks, cancelledTasks);
            return ServiceResult<TaskStatsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取任务统计数据时发生错误");
            return ServiceResult<TaskStatsResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<WorkerStatsResponse>> GetWorkerStatsAsync()
    {
        try
        {
            var totalWorkers = await dbContext.Workers.CountAsync();
            var onlineWorkers = await dbContext.Workers.CountAsync(w => w.Status == WorkerStatus.Online);
            var offlineWorkers = await dbContext.Workers.CountAsync(w => w.Status == WorkerStatus.Offline);
            var healthyWorkers = await dbContext.Workers.CountAsync(w => w.HealthStatus == WorkerHealthStatus.Healthy);
            var criticalWorkers = await dbContext.Workers.CountAsync(w => w.HealthStatus == WorkerHealthStatus.Critical);

            var averageLoad = await dbContext.WorkerMetrics
                .Where(m => m.RecordedAt >= DateTime.UtcNow.AddHours(-1))
                .AverageAsync(m => (double?)m.CpuUsagePercent) ?? 0.0;

            var loadData = await dbContext.Workers
                .Select(w => new WorkerLoadDataPoint(
                    w.Id,
                    w.Name,
                    w.Metrics.OrderByDescending(m => m.RecordedAt).FirstOrDefault() != null
                        ? w.Metrics.OrderByDescending(m => m.RecordedAt).First().CpuUsagePercent
                        : 0.0,
                    w.Metrics.OrderByDescending(m => m.RecordedAt).FirstOrDefault() != null
                        ? w.Metrics.OrderByDescending(m => m.RecordedAt).First().MemoryUsagePercent
                        : 0.0,
                    w.AssignedTasks.Count(t => t.Status == WorkerTaskStatus.Processing)))
                .ToListAsync();

            var response = new WorkerStatsResponse(
                totalWorkers,
                onlineWorkers,
                offlineWorkers,
                healthyWorkers,
                criticalWorkers,
                averageLoad,
                loadData);

            return ServiceResult<WorkerStatsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取工作节点统计数据时发生错误");
            return ServiceResult<WorkerStatsResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<ProxyStats>> GetProxyStatsAsync()
    {
        try
        {
            var totalProxies = await dbContext.Proxies.CountAsync();
            var activeProxies = await dbContext.Proxies.CountAsync(p => p.Status == ProxyStatus.Active);
            var healthyProxies = await dbContext.Proxies.CountAsync(p => p.HealthStatus == ProxyHealthStatus.Healthy);
            var unhealthyProxies = await dbContext.Proxies.CountAsync(p => p.HealthStatus == ProxyHealthStatus.Unhealthy);

            var healthRate = totalProxies > 0 ? (double)healthyProxies / totalProxies : 0.0;
            var lastHealthCheck = await dbContext.Proxies
                .Where(p => p.LastHealthCheckAt.HasValue)
                .MaxAsync(p => (DateTime?)p.LastHealthCheckAt) ?? DateTime.MinValue;

            var response = new ProxyStats(totalProxies, activeProxies, healthyProxies, unhealthyProxies, healthRate, lastHealthCheck);
            return ServiceResult<ProxyStats>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取代理统计数据时发生错误");
            return ServiceResult<ProxyStats>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<ContentStatsResponse>> GetContentStatsAsync(string timeRange)
    {
        try
        {
            var totalVideos = await dbContext.YouTubeVideos.CountAsync();
            var totalChannels = await dbContext.YouTubeChannels.CountAsync();
            var totalPlaylists = await dbContext.YouTubePlaylists.CountAsync();

            var blacklistedVideos = await dbContext.YouTubeVideos.CountAsync(v => v.IsBlacklisted);
            var blacklistedChannels = await dbContext.YouTubeChannels.CountAsync(c => c.IsBlacklisted);
            var blacklistedPlaylists = await dbContext.YouTubePlaylists.CountAsync(p => p.IsBlacklisted);

            var popularVideos = await dbContext.YouTubeVideos
                .OrderByDescending(v => v.DownloadCount)
                .Take(10)
                .Select(v => new PopularContentResponse(
                    v.VideoId,
                    "video",
                    v.Title ?? "",
                    v.DownloadCount,
                    v.LastDownloadedAt ?? DateTime.MinValue))
                .ToListAsync();

            var growthData = await GetContentGrowthDataAsync(timeRange);

            var response = new ContentStatsResponse(
                totalVideos,
                totalChannels,
                totalPlaylists,
                blacklistedVideos,
                blacklistedChannels,
                blacklistedPlaylists,
                popularVideos,
                growthData);

            return ServiceResult<ContentStatsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取内容统计数据时发生错误");
            return ServiceResult<ContentStatsResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<AlertListResponse>> GetAlertsAsync(int page, int pageSize)
    {
        try
        {
            var totalCount = await dbContext.WorkerAlerts.CountAsync();
            var skip = (page - 1) * pageSize;

            var alerts = await dbContext.WorkerAlerts
                .OrderByDescending(a => a.CreatedAt)
                .Skip(skip)
                .Take(pageSize)
                .Select(a => new AlertResponse(
                    a.Id,
                    a.AlertType.ToString(),
                    a.AlertLevel.ToString(),
                    a.Title,
                    a.Message,
                    a.WorkerId.ToString(),
                    new Dictionary<string, object>(),
                    a.IsResolved,
                    a.ResolvedAt,
                    "admin",
                    a.CreatedAt,
                    DateTime.UtcNow))
                .ToListAsync();

            var response = new AlertListResponse(alerts, totalCount, page, pageSize);
            return ServiceResult<AlertListResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取告警列表时发生错误");
            return ServiceResult<AlertListResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult> ResolveAlertAsync(Guid alertId)
    {
        try
        {
            var alert = await dbContext.WorkerAlerts.FindAsync(alertId);
            if (alert == null)
            {
                return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "告警不存在"));
            }

            alert.IsResolved = true;
            alert.ResolvedAt = DateTime.UtcNow;

            await dbContext.SaveChangesAsync();
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "确认告警时发生错误");
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult> CleanupOldDataAsync()
    {
        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-30);

            var oldMetrics = await dbContext.WorkerMetrics
                .Where(m => m.RecordedAt < cutoffDate)
                .ToListAsync();

            var oldAlerts = await dbContext.WorkerAlerts
                .Where(a => a.CreatedAt < cutoffDate && a.IsResolved)
                .ToListAsync();

            dbContext.WorkerMetrics.RemoveRange(oldMetrics);
            dbContext.WorkerAlerts.RemoveRange(oldAlerts);

            await dbContext.SaveChangesAsync();

            logger.LogInformation("已清理 {MetricsCount} 条过期监控数据和 {AlertsCount} 条过期告警记录",
                oldMetrics.Count, oldAlerts.Count);

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "清理过期数据时发生错误");
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    private async Task<SystemOverviewResponse> GetSystemOverviewAsync()
    {
        var totalUsers = await dbContext.Users.CountAsync();
        var activeUsers = await dbContext.Users.CountAsync(u => u.LastActiveAt.Date == DateTime.UtcNow.Date);
        var totalTasks = await dbContext.WorkerTasks.CountAsync();
        var processingTasks = await dbContext.WorkerTasks.CountAsync(t => t.Status == WorkerTaskStatus.Processing);
        var totalWorkers = await dbContext.Workers.CountAsync();
        var onlineWorkers = await dbContext.Workers.CountAsync(w => w.Status == WorkerStatus.Online);
        var totalProxies = await dbContext.Proxies.CountAsync();
        var healthyProxies = await dbContext.Proxies.CountAsync(p => p.HealthStatus == ProxyHealthStatus.Healthy);

        return new SystemOverviewResponse(
            totalUsers,
            activeUsers,
            totalTasks,
            processingTasks,
            totalWorkers,
            onlineWorkers,
            totalProxies,
            healthyProxies,
            1000L * 1024 * 1024 * 1024,
            250L * 1024 * 1024 * 1024);
    }

    private async Task<List<SystemComponentStatus>> GetComponentStatusesAsync()
    {
        var components = new List<SystemComponentStatus>();

        try
        {
            await dbContext.Database.CanConnectAsync();
            components.Add(new SystemComponentStatus("Database", "Healthy", null, DateTime.UtcNow));
        }
        catch (Exception ex)
        {
            components.Add(new SystemComponentStatus("Database", "Unhealthy", ex.Message, DateTime.UtcNow));
        }

        var onlineWorkers = await dbContext.Workers.CountAsync(w => w.Status == WorkerStatus.Online);
        var totalWorkers = await dbContext.Workers.CountAsync();
        components.Add(new SystemComponentStatus("Workers",
            onlineWorkers > 0 ? "Healthy" : "Warning",
            $"{onlineWorkers}/{totalWorkers} online",
            DateTime.UtcNow));

        var healthyProxies = await dbContext.Proxies.CountAsync(p => p.HealthStatus == ProxyHealthStatus.Healthy);
        var totalProxies = await dbContext.Proxies.CountAsync();
        components.Add(new SystemComponentStatus("Proxies",
            healthyProxies > totalProxies * 0.8 ? "Healthy" : "Warning",
            $"{healthyProxies}/{totalProxies} healthy",
            DateTime.UtcNow));

        return components;
    }

    private async Task<List<RecentActivityResponse>> GetRecentActivitiesAsync()
    {
        var activities = new List<RecentActivityResponse>();

        var recentTasks = await dbContext.WorkerTasks
            .OrderByDescending(t => t.CreatedAt)
            .Take(5)
            .Select(t => new RecentActivityResponse(
                "Task",
                $"任务 {t.Name} 已创建",
                t.CreatedAt,
                t.UserId.ToString()))
            .ToListAsync();

        activities.AddRange(recentTasks);

        var recentUsers = await dbContext.Users
            .Where(u => u.CreatedAt >= DateTime.UtcNow.AddHours(-24))
            .OrderByDescending(u => u.CreatedAt)
            .Take(3)
            .Select(u => new RecentActivityResponse(
                "User",
                $"新用户注册: {u.Email ?? "匿名用户"}",
                u.CreatedAt,
                u.Id.ToString()))
            .ToListAsync();

        activities.AddRange(recentUsers);

        return activities.OrderByDescending(a => a.Timestamp).Take(10).ToList();
    }

    private async Task<List<AlertSummaryResponse>> GetRecentAlertsAsync()
    {
        return await dbContext.WorkerAlerts
            .Where(a => !a.IsResolved)
            .OrderByDescending(a => a.CreatedAt)
            .Take(5)
            .Select(a => new AlertSummaryResponse(
                a.Id,
                a.AlertType.ToString(),
                a.Message,
                a.AlertLevel.ToString(),
                a.CreatedAt,
                a.IsResolved))
            .ToListAsync();
    }

    private async Task<HealthCheckResult> CheckDatabaseHealthAsync()
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        try
        {
            await dbContext.Database.CanConnectAsync();
            stopwatch.Stop();
            return new HealthCheckResult(
                "Database",
                true,
                "Connected",
                null,
                stopwatch.Elapsed,
                null);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            return new HealthCheckResult(
                "Database",
                false,
                "Connection Failed",
                ex.Message,
                stopwatch.Elapsed,
                null);
        }
    }

    private async Task<HealthCheckResult> CheckWorkersHealthAsync()
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        try
        {
            var totalWorkers = await dbContext.Workers.CountAsync();
            var onlineWorkers = await dbContext.Workers.CountAsync(w => w.Status == WorkerStatus.Online);
            stopwatch.Stop();

            var isHealthy = totalWorkers > 0 && onlineWorkers > 0;
            return new HealthCheckResult(
                "Workers",
                isHealthy,
                isHealthy ? "Workers Available" : "No Workers Online",
                isHealthy ? null : "No online workers available",
                stopwatch.Elapsed,
                new Dictionary<string, object>
                {
                    ["TotalWorkers"] = totalWorkers,
                    ["OnlineWorkers"] = onlineWorkers
                });
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            return new HealthCheckResult(
                "Workers",
                false,
                "Check Failed",
                ex.Message,
                stopwatch.Elapsed,
                null);
        }
    }

    private async Task<HealthCheckResult> CheckProxiesHealthAsync()
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        try
        {
            var totalProxies = await dbContext.Proxies.CountAsync();
            var healthyProxies = await dbContext.Proxies.CountAsync(p => p.HealthStatus == ProxyHealthStatus.Healthy);
            stopwatch.Stop();

            var healthRate = totalProxies > 0 ? (double)healthyProxies / totalProxies : 0;
            var isHealthy = healthRate > 0.5;

            return new HealthCheckResult(
                "Proxies",
                isHealthy,
                isHealthy ? "Proxies Available" : "Low Proxy Health",
                isHealthy ? null : "Less than 50% of proxies are healthy",
                stopwatch.Elapsed,
                new Dictionary<string, object>
                {
                    ["TotalProxies"] = totalProxies,
                    ["HealthyProxies"] = healthyProxies,
                    ["HealthRate"] = healthRate
                });
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            return new HealthCheckResult(
                "Proxies",
                false,
                "Check Failed",
                ex.Message,
                stopwatch.Elapsed,
                null);
        }
    }

    private async Task<List<UserGrowthDataPoint>> GetUserGrowthDataAsync(DateTime? startDate, DateTime? endDate, string? groupBy)
    {
        var start = startDate ?? DateTime.UtcNow.AddDays(-30);
        var end = endDate ?? DateTime.UtcNow;

        var users = await dbContext.Users
            .Where(u => u.CreatedAt >= start && u.CreatedAt <= end)
            .ToListAsync();

        var groupedData = groupBy?.ToLower() switch
        {
            "week" => users.GroupBy(u => u.CreatedAt.Date.AddDays(-(int)u.CreatedAt.DayOfWeek)),
            "month" => users.GroupBy(u => new DateTime(u.CreatedAt.Year, u.CreatedAt.Month, 1)),
            _ => users.GroupBy(u => u.CreatedAt.Date)
        };

        return groupedData.Select(g => new UserGrowthDataPoint(
            g.Key,
            g.Count(),
            g.Count(u => u.LastActiveAt.Date == g.Key),
            g.Count())).ToList();
    }

    private async Task<List<ContentGrowthDataPoint>> GetContentGrowthDataAsync(string timeRange)
    {
        var start = timeRange.ToLower() switch
        {
            "7d" => DateTime.UtcNow.AddDays(-7),
            "30d" => DateTime.UtcNow.AddDays(-30),
            "90d" => DateTime.UtcNow.AddDays(-90),
            _ => DateTime.UtcNow.AddDays(-30)
        };

        var videos = await dbContext.YouTubeVideos
            .Where(v => v.CreatedAt >= start)
            .GroupBy(v => v.CreatedAt.Date)
            .Select(g => new { Date = g.Key, Count = g.Count() })
            .ToListAsync();

        var channels = await dbContext.YouTubeChannels
            .Where(c => c.CreatedAt >= start)
            .GroupBy(c => c.CreatedAt.Date)
            .Select(g => new { Date = g.Key, Count = g.Count() })
            .ToListAsync();

        var playlists = await dbContext.YouTubePlaylists
            .Where(p => p.CreatedAt >= start)
            .GroupBy(p => p.CreatedAt.Date)
            .Select(g => new { Date = g.Key, Count = g.Count() })
            .ToListAsync();

        var allDates = videos.Select(v => v.Date)
            .Union(channels.Select(c => c.Date))
            .Union(playlists.Select(p => p.Date))
            .Distinct()
            .OrderBy(d => d);

        return allDates.Select(date => new ContentGrowthDataPoint(
            date,
            videos.FirstOrDefault(v => v.Date == date)?.Count ?? 0,
            channels.FirstOrDefault(c => c.Date == date)?.Count ?? 0,
            playlists.FirstOrDefault(p => p.Date == date)?.Count ?? 0)).ToList();
    }
}