using Shared.Common;

namespace Shared.DTOs;

public record AdminLoginRequest(string Email, string Password);

public record UpdateUserPlanRequest(UserPlanType PlanType, DateTime? PlanExpiresAt);

public record AdminUserListItem(
    Guid Id,
    UserType UserType,
    string? Email,
    UserPlanType PlanType,
    DateTime? PlanExpiresAt,
    UserAccountStatus Status,
    bool EmailVerified,
    DateTime? LastLoginAt,
    string? LastLoginIp,
    DateTime CreatedAt,
    DateTime LastActiveAt);

public record AdminUserListResponse(
    List<AdminUserListItem> Users,
    int TotalCount,
    int Page,
    int PageSize);

public record AdminUserDetailResponse(
    Guid Id,
    UserType UserType,
    string? Email,
    UserPlanType PlanType,
    DateTime? PlanExpiresAt,
    UserAccountStatus Status,
    bool EmailVerified,
    string? EmailVerificationToken,
    DateTime? EmailVerificationTokenExpiresAt,
    string? PasswordResetToken,
    DateTime? PasswordResetTokenExpiresAt,
    int SecurityVersion,
    int LoginFailureCount,
    DateTime? AccountLockedUntil,
    DateTime? LastLoginAt,
    string? LastLoginIp,
    DateTime CreatedAt,
    DateTime LastActiveAt,
    DateTime UpdatedAt,
    AdminUserStatsResponse Stats);

public record AdminUserStatsResponse(
    int TotalTasks,
    int CompletedTasks,
    int FailedTasks,
    int ActiveSessions,
    long TotalDownloadSize,
    DateTime? LastTaskAt);

public record AdminUserSessionResponse(
    Guid Id,
    Guid UserId,
    string? IpAddress,
    string? UserAgent,
    DateTime CreatedAt,
    DateTime ExpiresAt,
    bool IsExpired);

public record AdminUserSessionListResponse(
    List<AdminUserSessionResponse> Sessions,
    int TotalCount,
    int Page,
    int PageSize);

public record AdminUserTaskListResponse(
    List<WorkerTaskResponse> WorkerTasks,
    List<BatchTaskResponse> BatchTasks,
    int TotalCount,
    int Page,
    int PageSize);

public record AdminUserContentResponse(
    string VideoId,
    string? VideoTitle,
    string? VideoUrl,
    string? ThumbnailUrl,
    int TaskCount,
    DateTime FirstTaskAt,
    DateTime LastTaskAt);

public record AdminUserContentListResponse(
    List<AdminUserContentResponse> Content,
    int TotalCount,
    int Page,
    int PageSize);

public record AdminUserBillingResponse(
    Guid UserId,
    UserPlanType CurrentPlan,
    DateTime? CurrentPlanExpiresAt,
    List<AdminBillingHistoryResponse> History);

public record AdminBillingHistoryResponse(
    Guid Id,
    UserPlanType PlanType,
    DateTime StartDate,
    DateTime? EndDate,
    decimal Amount,
    string Currency,
    string Status,
    DateTime CreatedAt);

public record AdminUserListRequest(
    int Page = 1,
    int PageSize = 20,
    string? Search = null,
    UserType? UserType = null,
    UserAccountStatus? Status = null);

public record ResetPasswordResponse(
    string NewPassword,
    DateTime ResetAt);

public record DashboardDataResponse(
    SystemOverviewResponse SystemOverview,
    List<SystemComponentStatus> ComponentStatuses,
    List<RecentActivityResponse> RecentActivities,
    List<AlertSummaryResponse> RecentAlerts);

public record SystemOverviewResponse(
    int TotalUsers,
    int ActiveUsers,
    int TotalTasks,
    int ProcessingTasks,
    int TotalWorkers,
    int OnlineWorkers,
    int TotalProxies,
    int HealthyProxies,
    long TotalStorage,
    long UsedStorage);

public record SystemComponentStatus(
    string ComponentName,
    string Status,
    string? Message,
    DateTime LastChecked);

public record RecentActivityResponse(
    string ActivityType,
    string Description,
    DateTime Timestamp,
    string? UserId);

public record AlertSummaryResponse(
    Guid Id,
    string Type,
    string Message,
    string Severity,
    DateTime CreatedAt,
    bool IsAcknowledged);

public record SystemHealthResponse(
    bool IsHealthy,
    List<HealthCheckResult> Components,
    DateTime CheckedAt);

public record HealthCheckResult(
    string ComponentName,
    bool IsHealthy,
    string Status,
    string? ErrorMessage,
    TimeSpan ResponseTime,
    Dictionary<string, object>? AdditionalData);

public record SystemStatsResponse(
    CpuStatsResponse Cpu,
    MemoryStatsResponse Memory,
    DiskStatsResponse Disk,
    NetworkStatsResponse Network,
    DateTime Timestamp);

public record CpuStatsResponse(
    double UsagePercentage,
    int CoreCount,
    double LoadAverage);

public record MemoryStatsResponse(
    long TotalBytes,
    long UsedBytes,
    long AvailableBytes,
    double UsagePercentage);

public record DiskStatsResponse(
    long TotalBytes,
    long UsedBytes,
    long AvailableBytes,
    double UsagePercentage);

public record NetworkStatsResponse(
    long BytesReceived,
    long BytesSent,
    long PacketsReceived,
    long PacketsSent);

public record UserStatsResponse(
    int TotalUsers,
    int NewUsersToday,
    int ActiveUsersToday,
    int RegisteredUsers,
    int AnonymousUsers,
    List<UserGrowthDataPoint> GrowthData);

public record UserGrowthDataPoint(
    DateTime Date,
    int NewUsers,
    int ActiveUsers,
    int TotalUsers);

public record WorkerStatsResponse(
    int TotalWorkers,
    int OnlineWorkers,
    int OfflineWorkers,
    int HealthyWorkers,
    int UnhealthyWorkers,
    double AverageLoad,
    List<WorkerLoadDataPoint> LoadData);

public record WorkerLoadDataPoint(
    Guid WorkerId,
    string WorkerName,
    double CpuUsage,
    double MemoryUsage,
    int ActiveTasks);

public record ContentStatsResponse(
    int TotalVideos,
    int TotalChannels,
    int TotalPlaylists,
    int BlacklistedVideos,
    int BlacklistedChannels,
    int BlacklistedPlaylists,
    List<PopularContentResponse> PopularVideos,
    List<ContentGrowthDataPoint> GrowthData);

public record PopularContentResponse(
    string ContentId,
    string ContentType,
    string Title,
    int DownloadCount,
    DateTime LastDownloaded);

public record ContentGrowthDataPoint(
    DateTime Date,
    int NewVideos,
    int NewChannels,
    int NewPlaylists);

public record AlertResponse(
    Guid Id,
    string Type,
    string Severity,
    string Title,
    string Message,
    string? Source,
    Dictionary<string, object>? Metadata,
    bool IsAcknowledged,
    DateTime? AcknowledgedAt,
    string? AcknowledgedBy,
    DateTime CreatedAt,
    DateTime UpdatedAt);

public record AlertListResponse(
    List<AlertResponse> Alerts,
    int TotalCount,
    int Page,
    int PageSize);

public record ContentListResponse(
    List<ContentItem> Items,
    int TotalCount,
    int Page,
    int PageSize);

public record ContentItem(
    string Id,
    string Type,
    string Title,
    string ChannelName,
    string? Thumbnail,
    bool IsBlacklisted,
    DateTime CreatedAt);

public record TaskListResponse(
    List<WorkerTaskResponse> Tasks,
    int TotalCount,
    int Page,
    int PageSize);

public record WorkerListResponse(
    List<WorkerResponse> Workers,
    int TotalCount,
    int Page,
    int PageSize);

public record WorkerDetailResponse(
    Guid Id,
    string Name,
    string BaseUrl,
    string? MachineName,
    int CpuCores,
    double TotalMemoryGB,
    double TotalDiskGB,
    WorkerStatus Status,
    WorkerHealthStatus HealthStatus,
    int TotalProcessedTasks,
    int ConsecutiveFailures,
    DateTime LastActiveAt,
    DateTime CreatedAt,
    List<WorkerMetricResponse> RecentMetrics,
    List<WorkerTaskSummary> ActiveTasks);

public record WorkerMetricResponse(
    double CpuUsagePercent,
    double MemoryUsagePercent,
    double DiskUsagePercent,
    double NetworkReceivedGB,
    double NetworkSentGB,
    double NetworkBandwidthMbps,
    int ActiveConnections,
    DateTime RecordedAt);

public record WorkerTaskSummary(
    Guid Id,
    string Name,
    WorkerTaskStatus Status,
    int Progress,
    DateTime CreatedAt);

public record WorkerMetricsResponse(
    Guid WorkerId,
    List<WorkerMetricResponse> Metrics);

public record UpdateWorkerRequest(
    string Name);